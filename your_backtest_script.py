# Save this as your_backtest_script.py

import backtrader as bt
import datetime

# --- Step 3: Define Your Data Feed in Backtrader ---
# All Yahoo Finance related lines are commented out as we are using intraday CSV data.
# ticker_symbol = 'EURUSD=X'
# start_date = datetime.datetime(2023, 1, 1)
# end_date = datetime.datetime.now()
# data_daily = bt.feeds.YahooFinanceData(
#     dataname=ticker_symbol,
#     fromdate=start_date,
#     todate=end_date
# )
# print(f"Daily Data feed for {ticker_symbol} from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')} defined.")

# --- Step 4: Create Your Strategy Class (Tokyo Sweep Reversal Strategy) ---
class TokyoSweepReversalStrategy(bt.Strategy):
    # Define strategy parameters
    params = (
        ('tokyo_start_hour_gmt', 0),    # 12 AM GMT
        ('tokyo_end_hour_gmt', 6),      # 6 AM GMT
        ('london_entry_start_hour_gmt', 6), # 6 AM GMT
        ('london_entry_end_hour_gmt', 8.5), # 8:30 AM GMT (8.5 means 8 hours 30 mins)
        
        # Risk Management Parameters
        ('risk_per_trade_dollars', 50), # Max dollar amount to risk per trade
        ('stop_loss_pips', 15),         # Stop Loss distance in pips
        ('take_profit_pips', 45),       # Take Profit distance in pips (1:3 Risk:Reward based on pips)
        ('pip_value', 0.0001),          # Value of 1 pip for 1 unit (e.g., for EUR/USD, 0.0001 USD per unit)
        ('max_drawdown_dollars', 400), # Max dollar drawdown from peak equity to stop backtest
    )

    def __init__(self):
        # Get reference to both data feeds
        self.data_15m = self.datas[0]  # 15M data (for sweep detection)
        self.data_5m = self.datas[1]   # 5M data (primary for entry and execution)
        
        # Set the 5M data as the primary data stream for strategy logic
        self.primary_data = self.data_5m

        # Store lines for easier access (using 5M data as primary for OHLC)
        self.dataclose = self.primary_data.close
        self.dataopen = self.primary_data.open
        self.datahigh = self.primary_data.high
        self.datalow = self.primary_data.low

        # To keep track of pending orders (entry, SL, TP)
        self.order = None
        self.sl_order = None
        self.tp_order = None

        # Variables to track Tokyo session high/low for the previous completed Tokyo session
        self.tokyo_session_high = None
        self.tokyo_session_low = None
        self.last_tokyo_day = None # To track when a new Tokyo session starts

        # Flag to indicate if a sweep has been detected
        self.sweep_detected = False
        self.sweep_direction = None # 'up' or 'down'

        # Calculate dynamic stake based on risk and stop loss distance
        # Example: $50 risk / (15 pips * $0.0001/unit/pip) = 33333.33 units
        if self.p.stop_loss_pips == 0: # Avoid division by zero
            self.stake_units = 1000 # Fallback, should ideally not happen
            self.log("WARNING: Stop Loss Pips is 0, setting stake to 1000 units. Please review your parameters.")
        else:
            self.stake_units = int(self.p.risk_per_trade_dollars / (self.p.stop_loss_pips * self.p.pip_value))
        
        # Max drawdown tracking
        self.peak_value = self.broker.getvalue() # Initialize with current cash
        self.log(f'Initial Portfolio Value: ${self.peak_value:.2f}') # Log initial value
        self.log(f'Calculated Stake: {self.stake_units} units per trade (based on ${self.p.risk_per_trade_dollars} risk and {self.p.stop_loss_pips} pips SL)')


    def log(self, txt, dt=None):
        ''' Logging function for this strategy'''
        dt = dt or self.primary_data.datetime.datetime(0) # Use datetime.datetime(0) for full timestamp
        print(f'{dt.isoformat()}, {txt}')

    def notify_order(self, order):
        if order.status in [order.Submitted, order.Accepted]:
            # Order submitted/accepted - Nothing to do yet, just log
            self.log(f'{order.getstatusname(order.status)} Order (Ref: {order.ref}, Type: {order.info.get("placetype", "Entry")})')
            return

        if order.status in [order.Completed]:
            if order.isbuy():
                self.log(
                    f'BUY EXECUTED (Ref: {order.ref}, Type: {order.info.get("placetype", "Entry")}), Price: {order.executed.price:.5f}, Size: {order.executed.size}, Cost: {order.executed.value:.2f}, Comm: {order.executed.comm:.2f}'
                )
            elif order.issell():
                self.log(
                    f'SELL EXECUTED (Ref: {order.ref}, Type: {order.info.get("placetype", "Entry")}), Price: {order.executed.price:.5f}, Size: {order.executed.size}, Cost: {order.executed.value:.2f}, Comm: {order.executed.comm:.2f}'
                )
            
            # If the entry order completed, clear the pending entry order reference
            if order.info.get('placetype') == 'entry':
                self.order = None 
                # Now that entry is complete, place SL/TP orders
                self.place_sl_tp(order.executed.price, order.isbuy())

        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log(f'Order Canceled/Margin/Rejected (Ref: {order.ref}, Type: {order.info.get("placetype", "Entry")}, Status: {order.getstatusname(order.status)})')
            # If an entry order is cancelled, clear pending SL/TP as well
            if order.info.get('placetype') == 'entry':
                self.order = None
                if self.sl_order and self.sl_order.status < self.sl_order.Completed: self.cancel(self.sl_order)
                if self.tp_order and self.tp_order.status < self.tp_order.Completed: self.cancel(self.tp_order)
                self.sl_order = None
                self.tp_order = None
        
        # If SL/TP order completed, ensure internal references are cleared
        # This handles cases where an SL/TP closes the trade
        if order.info.get('placetype') == 'stoploss' and order.status == order.Completed:
            self.sl_order = None
            self.log(f'STOP LOSS Triggered: {order.executed.price:.5f}')
        if order.info.get('placetype') == 'takeprofit' and order.status == order.Completed:
            self.tp_order = None
            self.log(f'TAKE PROFIT Triggered: {order.executed.price:.5f}')

    def notify_trade(self, trade):
        if not trade.isclosed:
            return # Trade is still open

        # Log P&L only when the trade is closed
        if trade.isclosed:
            self.log(f'TRADE CLOSED, GROSS P&L: {trade.pnl:.2f}, NET P&L: {trade.pnlcomm:.2f}')
            # Ensure any pending SL/TP orders for this closed trade are cancelled
            if self.sl_order and self.sl_order.status < self.sl_order.Completed: self.cancel(self.sl_order)
            if self.tp_order and self.tp_order.status < self.tp_order.Completed: self.cancel(self.tp_order)
            self.sl_order = None
            self.tp_order = None


    def notify_cashvalue(self, cash, value):
        ''' Called on every new bar with updated cash and portfolio value '''
        # Update peak value if current value is higher
        self.peak_value = max(self.peak_value, value)

        current_drawdown = self.peak_value - value

        # Log drawdown if it's significant or if we're stopping
        if current_drawdown > 0 and current_drawdown >= self.p.max_drawdown_dollars:
            self.log(f'MAX DRAWDOWN REACHED! ${self.p.max_drawdown_dollars:.2f} (Current Drawdown: ${current_drawdown:.2f}) - Stopping Backtest.')
            self.broker.stop() # Stops the backtest immediately
        elif current_drawdown > 0 and current_drawdown > 500: # Log significant drawdown without stopping
             self.log(f'Current Drawdown: ${current_drawdown:.2f}, Peak Value: ${self.peak_value:.2f}, Current Value: ${value:.2f}')


    def place_sl_tp(self, entry_price, is_buy):
        ''' Places stop loss and take profit orders after an entry. '''
        
        # Determine current position size (should be self.stake_units or what was executed)
        # Use abs(self.position.size) to handle sell orders having negative size
        position_size = abs(self.position.size) 

        # Calculate SL/TP prices based on pips
        if is_buy:
            sl_price = entry_price - (self.p.stop_loss_pips * self.p.pip_value)
            tp_price = entry_price + (self.p.take_profit_pips * self.p.pip_value)
            
            # Place SL order (sell at stop price)
            self.sl_order = self.sell(
                exectype=bt.Order.StopLimit,
                price=sl_price,             # Stop price
                plimit=sl_price,            # Limit price (same as stop for a market-like stop)
                size=position_size,
                info={'placetype': 'stoploss'}
            )
            # Place TP order (sell at limit price)
            self.tp_order = self.sell(
                exectype=bt.Order.Limit,
                price=tp_price,             # Limit price
                size=position_size,
                info={'placetype': 'takeprofit'}
            )
            self.log(f'Placed BUY SL: {sl_price:.5f}, TP: {tp_price:.5f} (Size: {position_size})')

        else: # is_sell
            sl_price = entry_price + (self.p.stop_loss_pips * self.p.pip_value)
            tp_price = entry_price - (self.p.take_profit_pips * self.p.pip_value)
            
            # Place SL order (buy at stop price)
            self.sl_order = self.buy(
                exectype=bt.Order.StopLimit,
                price=sl_price,             # Stop price
                plimit=sl_price,            # Limit price (same as stop for a market-like stop)
                size=position_size,
                info={'placetype': 'stoploss'}
            )
            # Place TP order (buy at limit price)
            self.tp_order = self.buy(
                exectype=bt.Order.Limit,
                price=tp_price,             # Limit price
                size=position_size,
                info={'placetype': 'takeprofit'}
            )
            self.log(f'Placed SELL SL: {sl_price:.5f}, TP: {tp_price:.5f} (Size: {position_size})')


    def next(self):
        # Access the current datetime for the primary (5M) timeframe
        current_dt = self.primary_data.datetime.datetime(0)
        current_hour_gmt = current_dt.hour
        current_minute_gmt = current_dt.minute

        # IMPORTANT: Only print current bar log if it's the *first* data point of the current bar
        # This prevents duplicate 'Current Bar' logs when multiple data feeds are processed on the same bar
        if len(self.primary_data) > 1 and \
           self.primary_data.datetime.datetime(-1).minute != current_dt.minute: # Check if previous bar was a different minute
            self.log(f'Current Bar - Close: {self.dataclose[0]:.5f}')
        elif len(self.primary_data) == 1: # For the very first bar
            self.log(f'Current Bar - Close: {self.dataclose[0]:.5f}')

        # Check if an entry order is pending. If so, do not issue new entry orders.
        if self.order:
            return

        # Check if we are already in a position. If so, just monitor it (SL/TP will handle exits)
        if self.position:
            # We are in a trade, SL/TP orders are already placed and will handle exits.
            # No new entries or sweep detection needed for this bar.
            return

        # --- 1. Tokyo Session High/Low Tracking ---
        # We need to know if this is the start of a new day for Tokyo session tracking
        if self.last_tokyo_day is None or current_dt.day != self.last_tokyo_day:
            # self.log(f'New Day: {current_dt.isoformat()}') # Can be noisy, uncomment if needed
            # Reset for a new day
            self.tokyo_session_high = None # Reset to None so the first bar of the session initializes it
            self.tokyo_session_low = None  # Reset to None
            self.sweep_detected = False
            self.sweep_direction = None
            self.last_tokyo_day = current_dt.day

        # If within Tokyo session (12 AM - 6 AM GMT)
        if self.p.tokyo_start_hour_gmt <= current_hour_gmt < self.p.tokyo_end_hour_gmt:
            # Update Tokyo session high/low using the primary data (5M)
            # Make sure we have at least one bar to get high/low from
            if len(self.primary_data) > 0:
                if self.tokyo_session_high is None: # Initialize with first high/low of the session
                    self.tokyo_session_high = self.datahigh[0]
                    self.tokyo_session_low = self.datalow[0]
                else:
                    self.tokyo_session_high = max(self.tokyo_session_high, self.datahigh[0])
                    self.tokyo_session_low = min(self.tokyo_session_low, self.datalow[0])
            return # Do not trade during Tokyo session, just track high/low

        # --- 2. Sweep Detection ---
        # Only check for sweeps if we have valid Tokyo session data and haven't detected a sweep yet
        if (not self.sweep_detected and
            self.tokyo_session_high is not None and
            self.tokyo_session_low is not None and
            self.tokyo_session_high > self.tokyo_session_low): # Sanity check: ensure high is above low
            
            # Using current 5M bar high/low for sweep detection
            if self.datahigh[0] > self.tokyo_session_high:
                self.log(f'Tokyo High Swept! Price: {self.datahigh[0]:.5f} > Tokyo High: {self.tokyo_session_high:.5f}')
                self.sweep_detected = True
                self.sweep_direction = 'up'
            elif self.datalow[0] < self.tokyo_session_low:
                self.log(f'Tokyo Low Swept! Price: {self.datalow[0]:.5f} < Tokyo Low: {self.tokyo_session_low:.5f}')
                self.sweep_detected = True
                self.sweep_direction = 'down'

        # --- 3. Entry Logic (London Open window, 5M data) ---
        # Convert current time to decimal hours for comparison with london_entry_end_hour_gmt (8.5)
        current_time_decimal = current_hour_gmt + (current_minute_gmt / 60.0)

        if self.p.london_entry_start_hour_gmt <= current_time_decimal < self.p.london_entry_end_hour_gmt:
            if self.sweep_detected:
                if self.sweep_direction == 'up':
                    # Logic for rejection after sweeping Tokyo High (looking for sell signal)
                    # Simple bearish rejection: current bar closes below open
                    if self.dataclose[0] < self.dataopen[0]:
                        self.log(f'SELL CREATE (Tokyo High Sweep Rejection), {self.dataclose[0]:.5f} (Size: {self.stake_units})')
                        # Place entry order, tagging it with 'entry' for notify_order
                        self.order = self.sell(size=self.stake_units, info={'placetype': 'entry'})

                elif self.sweep_direction == 'down':
                    # Logic for rejection after sweeping Tokyo Low (looking for buy signal)
                    # Simple bullish rejection: current bar closes above open
                    if self.dataclose[0] > self.dataopen[0]:
                        self.log(f'BUY CREATE (Tokyo Low Sweep Rejection), {self.dataclose[0]:.5f} (Size: {self.stake_units})')
                        # Place entry order, tagging it with 'entry' for notify_order
                        self.order = self.buy(size=self.stake_units, info={'placetype': 'entry'})

# --- Cerebro Setup ---
cerebro = bt.Cerebro()

# Add your strategy
cerebro.addstrategy(TokyoSweepReversalStrategy)

# --- Configuration for your INTRADAY Data Feeds ---
csv_15m_path = 'eurusd_15m.csv'
csv_5m_path = 'eurusd_5m.csv'

# Define the date format for your CSV. Dukascopy usually uses 'DD.MM.YYYY HH:MM:SS.millis GMT+ZZZZ'
dt_format_str = '%d.%m.%Y %H:%M:%S.%f GMT%z' # Corrected format for milliseconds and dynamic timezone offset

# Define column mapping for your CSV.
# Assuming Dukascopy CSV columns are: DateTime, Open, High, Low, Close, Volume
column_mapping = {
    'datetime': 0,
    'open': 1,
    'high': 2,
    'low': 3,
    'close': 4,
    'volume': 5,
    'openinterest': -1 # Not present in Dukascopy CSV by default
}

try:
    # Add the 15-minute data feed (will be datas[0] in strategy)
    data_15m = bt.feeds.GenericCSVData(
        dataname=csv_15m_path,
        fromdate=datetime.datetime(2023, 1, 1), # IMPORTANT: Adjust to your data's actual start date
        todate=datetime.datetime(2025, 5, 31), # IMPORTANT: Adjust to your data's actual end date (e.g., May 31, 2025)
        dtformat=dt_format_str,
        timeframe=bt.TimeFrame.Minutes,
        compression=15,
        **column_mapping,
        headers=True
    )
    cerebro.adddata(data_15m, name='15M_Data')

    # Add the 5-minute data feed (will be datas[1] in strategy)
    data_5m = bt.feeds.GenericCSVData(
        dataname=csv_5m_path,
        fromdate=datetime.datetime(2023, 1, 1), # IMPORTANT: Adjust to your data's actual start date
        todate=datetime.datetime(2025, 5, 31), # IMPORTANT: Adjust to your data's actual end date
        dtformat=dt_format_str,
        timeframe=bt.TimeFrame.Minutes,
        compression=5,
        **column_mapping,
        headers=True
    )
    cerebro.adddata(data_5m, name='5M_Data')

    # Configure broker and initial capital
    cerebro.broker.setcash(10000.0) # Starting capital: $10,000 as requested
    cerebro.broker.setcommission(commission=0.00007) # Example commission for EUR/USD (0.7 pips per standard lot round turn)

    # Use a FixedSize sizer: The stake is calculated within the strategy's __init__
    # Backtrader requires the sizer to be added before run. We'll pass the *calculated* stake from parameters.
    # Note: This means the sizer here will use the *default* parameter values for calculation,
    # and the strategy's __init__ will perform the calculation again for its internal `self.stake_units`.
    # It's best to keep them consistent.
    # The stake needs to be an integer.
    calculated_initial_stake_for_sizer = int(TokyoSweepReversalStrategy.params.risk_per_trade_dollars /
                                             (TokyoSweepReversalStrategy.params.stop_loss_pips * TokyoSweepReversalStrategy.params.pip_value))
    cerebro.addsizer(bt.sizers.FixedSize, stake=calculated_initial_stake_for_sizer)


    print(f'Starting Portfolio Value: {cerebro.broker.getvalue():.2f}')

    # Run the backtest
    results = cerebro.run()

    print(f'Final Portfolio Value: {cerebro.broker.getvalue():.2f}')

    # Optional: Plot the results (requires matplotlib and a display environment)
    # cerebro.plot()

except FileNotFoundError as e:
    print(f"\nError: Data file not found. Please ensure '{e.filename}' exists in the script directory.")
    print("You need to download the intraday EUR/USD CSV data (e.g., from Dukascopy) and place it here.")
except Exception as e:
    print(f"\nAn error occurred during backtest setup or run: {e}")